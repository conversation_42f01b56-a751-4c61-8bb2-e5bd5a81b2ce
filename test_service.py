"""
Test script for the FastAPI DroidRun service
"""

import asyncio
import json
import uuid
import httpx
from datetime import datetime


async def test_run_task():
    """Test the run task endpoint"""
    
    # Test data
    test_request = {
        "v": "1.0.0",
        "auth": {
            "appId": "wbb2dej3",
            "nonce": "g1wbfhdx9IkDBOVQFHFkPjUUxYijErxT"
        },
        "arg": {
            "site": "eac-android-any",
            "eacId": "eac-0112",
            "jobId": f"job-{int(datetime.now().timestamp() * 1000)}",
            "eventType": "Autonomous",
            "content": "查看安卓版本号",
            "device": "cyojfeayhqytaypj"
        }
    }
    
    async with httpx.AsyncClient() as client:
        try:
            print("🚀 Testing run task endpoint...")
            print(f"Request: {json.dumps(test_request, indent=2)}")
            
            response = await client.post(
                "http://localhost:43826/eac/aia/run-task",
                json=test_request,
                timeout=30.0
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            
            if response.status_code == 200:
                print("✅ Task submitted successfully!")
                return test_request["arg"]["jobId"]
            else:
                print("❌ Task submission failed!")
                return None
                
        except Exception as e:
            print(f"❌ Error testing run task: {e}")
            return None


async def test_stop_task(job_id: str):
    """Test the stop task endpoint"""
    
    # Wait a bit before stopping
    await asyncio.sleep(2)
    
    stop_request = {
        "v": "1.0.0",
        "auth": {
            "appId": "wbb2dej3",
            "nonce": str(uuid.uuid4())
        },
        "arg": {
            "jobId": job_id
        }
    }
    
    async with httpx.AsyncClient() as client:
        try:
            print("🛑 Testing stop task endpoint...")
            print(f"Request: {json.dumps(stop_request, indent=2)}")
            
            response = await client.post(
                "http://localhost:43826/eac/aia/stop-task",
                json=stop_request,
                timeout=30.0
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            
            if response.status_code == 200:
                print("✅ Task stop request successful!")
            else:
                print("❌ Task stop request failed!")
                
        except Exception as e:
            print(f"❌ Error testing stop task: {e}")


async def test_health_check():
    """Test the health check endpoint"""
    
    async with httpx.AsyncClient() as client:
        try:
            print("🏥 Testing health check endpoint...")
            
            response = await client.get(
                "http://localhost:43826/health",
                timeout=10.0
            )
            
            print(f"Status Code: {response.status_code}")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
            
            if response.status_code == 200:
                print("✅ Health check successful!")
            else:
                print("❌ Health check failed!")
                
        except Exception as e:
            print(f"❌ Error testing health check: {e}")


async def main():
    """Run all tests"""
    print("🧪 Starting FastAPI DroidRun service tests...\n")
    
    # Test health check first
    await test_health_check()
    print()
    
    # Test run task
    job_id = await test_run_task()
    print()
    
    # Test stop task if we got a job ID
    if job_id:
        await test_stop_task(job_id)
        print()
    
    print("🏁 Tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
