"""
Pydantic models for FastAPI request/response schemas
"""

from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AuthModel(BaseModel):
    """Authentication model"""
    appId: str = Field(..., description="Application ID")
    nonce: str = Field(..., description="Random nonce for request")


class RunTaskArg(BaseModel):
    """Arguments for run task request"""
    site: str = Field(..., description="Site identifier")
    eacId: str = Field(..., description="EAC identifier")
    jobId: str = Field(..., description="Unique job identifier")
    eventType: str = Field(..., description="Event type")
    content: str = Field(..., description="Task goal/content")
    device: str = Field(..., description="Device serial number")
    callbackUrl: Optional[str] = Field(None, description="Callback URL for results")


class RunTaskRequest(BaseModel):
    """Request model for run task endpoint"""
    v: str = Field(..., description="API version")
    auth: AuthModel = Field(..., description="Authentication data")
    arg: RunTaskArg = Field(..., description="Task arguments")


class StopTaskArg(BaseModel):
    """Arguments for stop task request"""
    jobId: str = Field(..., description="Job ID to stop")


class StopTaskRequest(BaseModel):
    """Request model for stop task endpoint"""
    v: str = Field(..., description="API version")
    auth: AuthModel = Field(..., description="Authentication data")
    arg: StopTaskArg = Field(..., description="Stop task arguments")


class StandardResponse(BaseModel):
    """Standard response format"""
    code: int = Field(..., description="Response code (0=success, non-zero=error)")
    msg: str = Field(..., description="Response message")
    data: Dict[str, Any] = Field(..., description="Response data")


class CallbackArg(BaseModel):
    """Arguments for callback request"""
    eacId: str = Field(..., description="EAC identifier")
    jobId: str = Field(..., description="Job identifier")
    eventType: str = Field(..., description="Event type")
    state: int = Field(..., description="Task state (1=success, 3=failure)")
    msg: str = Field(..., description="Result message")


class CallbackRequest(BaseModel):
    """Request model for callback notification"""
    v: str = Field(..., description="API version")
    auth: AuthModel = Field(..., description="Authentication data")
    arg: CallbackArg = Field(..., description="Callback arguments")


class CallbackResponse(BaseModel):
    """Response model for callback"""
    errorCode: int = Field(..., description="Error code")
    errorMessage: str = Field(..., description="Error message")


class TaskInfo(BaseModel):
    """Task information model"""
    job_id: str = Field(..., description="Job identifier")
    content: str = Field(..., description="Task content")
    device_serial: str = Field(..., description="Device serial number")
    status: TaskStatus = Field(..., description="Task status")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    eac_id: Optional[str] = Field(None, description="EAC identifier")
    callback_url: Optional[str] = Field(None, description="Callback URL")
    result_message: Optional[str] = Field(None, description="Result or error message")
