import asyncio
from llama_index.llms.gemini import <PERSON>
from llama_index.llms.deepseek import DeepSeek
from droidrun.agent.droid import DroidAgent
from droidrun.tools import load_tools

async def main():
    # Load tools (load_tools is not async)
    tool_list, tools_instance = load_tools()
    
    # Create LlamaIndex LLM directly
    llm = DeepSeek(
        model="deepseek-v3-250324",
        api_key="1444135e-36b0-4f40-9130-7524530ea32c",
        api_base="https://ark.cn-beijing.volces.com/api/v3",
        temperature=0.2
    )
    
    task = "Open the Settings app and check the Android version"
    task = "打开美团买一杯咖啡"

    # Create and run the agent
    agent = DroidAgent(
        goal=task,
        llm=llm,
        max_steps=15,
        device_serial="cyojfeayhqytaypj",
        timeout=1000,
        max_retries=3
    )
    
    # Run the agent
    result = await agent.run()
    print(f"Success: {result['success']}")

if __name__ == "__main__":
    asyncio.run(main())
