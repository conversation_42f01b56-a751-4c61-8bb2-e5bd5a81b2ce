# DroidRun FastAPI HTTP Service

A FastAPI-based HTTP service that wraps DroidRun functionality for asynchronous Android automation task execution.

## Features

- **Asynchronous Task Execution**: Submit tasks and get immediate responses while tasks run in background
- **Task Management**: Track task status, cancel running tasks
- **Callback Support**: Automatic result notification via HTTP callbacks
- **Standardized API**: Follows specified request/response format
- **Error Handling**: Comprehensive error handling and logging

## API Endpoints

### 1. Submit Task - `POST /eac/aia/run-task`

Submit a task for asynchronous execution.

**Request Format:**
```json
{
  "v": "1.0.0",
  "auth": {
    "appId": "wbb2dej3",
    "nonce": "g1wbfhdx9IkDBOVQFHFkPjUUxYijErxT"
  },
  "arg": {
    "site": "eac-android-any",
    "eacId": "eac-0112",
    "jobId": "job-2933236482858576299",
    "eventType": "Autonomous",
    "content": "查看安卓版本号",
    "device": "cyojfeayhqytaypj",
    "callbackUrl": "http://your-server.com/callback" // Optional
  }
}
```

**Response Format:**
```json
{
  "code": 0,
  "msg": "Task submitted successfully",
  "data": {}
}
```

### 2. Stop Task - `POST /eac/aia/stop-task`

Cancel a running task.

**Request Format:**
```json
{
  "v": "1.0.0",
  "auth": {
    "appId": "wbb2dej3",
    "nonce": "g1wbfhdx9IkDBOVQFHFkPjUUxYijErxT"
  },
  "arg": {
    "jobId": "job-2933236482858576299"
  }
}
```

**Response Format:**
```json
{
  "code": 0,
  "msg": "Task cancelled successfully",
  "data": {}
}
```

### 3. Health Check - `GET /health`

Check service health status.

**Response Format:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

## Callback Notification

When a task completes (success or failure), the service will send a callback to the specified URL:

**Callback Request Format:**
```json
{
  "v": "1.0.0",
  "auth": {
    "appId": "wbb2dej3",
    "nonce": "generated-nonce"
  },
  "arg": {
    "eacId": "eac-0112",
    "jobId": "job-2933236482858576299",
    "eventType": "Autonomous",
    "state": 1,  // 1=success, 3=failure
    "msg": "执行结果是 xxx"
  }
}
```

**Expected Callback Response:**
```json
{
  "errorCode": 0,
  "errorMessage": "succ"
}
```

## Installation & Setup

1. **Install Dependencies:**
   ```bash
   pip install -e .
   ```

2. **Set Environment Variables (Optional):**
   ```bash
   export DEEPSEEK_API_KEY="your-api-key"
   export DEEPSEEK_MODEL="deepseek-v3-250324"
   export DEEPSEEK_API_BASE="https://ark.cn-beijing.volces.com/api/v3"
   export MAX_STEPS="15"
   export TASK_TIMEOUT="1000"
   ```

3. **Run the Service:**
   ```bash
   uvicorn app:app --host 0.0.0.0 --port 43826 --reload
   ```

## Configuration

The service can be configured via environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `HOST` | `0.0.0.0` | Server host |
| `PORT` | `43826` | Server port |
| `DEEPSEEK_API_KEY` | Required | DeepSeek API key |
| `DEEPSEEK_MODEL` | `deepseek-v3-250324` | DeepSeek model name |
| `DEEPSEEK_API_BASE` | `https://ark.cn-beijing.volces.com/api/v3` | DeepSeek API base URL |
| `DEEPSEEK_TEMPERATURE` | `0.2` | LLM temperature |
| `MAX_STEPS` | `15` | Maximum steps per task |
| `TASK_TIMEOUT` | `1000` | Task timeout in seconds |
| `MAX_RETRIES` | `3` | Maximum retries per task |
| `LOG_LEVEL` | `INFO` | Logging level |

## Testing

Run the test script to verify the service:

```bash
python test_service.py
```

This will test:
- Health check endpoint
- Task submission
- Task cancellation

## Task Flow

1. **Submit Task**: Client sends POST request to `/eac/aia/run-task`
2. **Immediate Response**: Service returns success response immediately
3. **Background Execution**: Task runs asynchronously using DroidAgent
4. **Status Tracking**: Task status is tracked internally
5. **Callback Notification**: On completion, service calls callback URL with results
6. **Cancellation Support**: Tasks can be cancelled via `/eac/aia/stop-task`

## Error Handling

- **400 Bad Request**: Invalid request format or missing required fields
- **404 Not Found**: Task not found (for stop requests)
- **409 Conflict**: Task with same job ID already exists
- **500 Internal Server Error**: Service error during task execution

## Logging

The service provides comprehensive logging:
- Task submission and execution
- Callback notifications
- Error conditions
- Task status changes

## Architecture

```
Client Request → FastAPI → Task Manager → DroidAgent → Android Device
                    ↓
              Callback URL ← HTTP Client ← Task Completion
```

## Dependencies

- FastAPI: Web framework
- Uvicorn: ASGI server
- Pydantic: Data validation
- httpx: HTTP client for callbacks
- DroidRun: Android automation framework
