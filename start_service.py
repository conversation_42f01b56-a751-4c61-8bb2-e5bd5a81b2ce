#!/usr/bin/env python3
"""
Startup script for the DroidRun FastAPI service
"""

import os
import sys
import subprocess
from pathlib import Path


def main():
    """Start the DroidRun FastAPI service"""
    
    # Ensure we're in the correct directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("🚀 Starting DroidRun FastAPI HTTP Service...")
    print(f"📁 Working directory: {script_dir}")
    print("🌐 Service will be available at: http://0.0.0.0:43826")
    print("📖 API documentation at: http://0.0.0.0:43826/docs")
    print("🔄 Auto-reload enabled for development")
    print()
    
    # Check if required files exist
    required_files = ["app.py", "models.py", "task_manager.py", "config.py"]
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        sys.exit(1)
    
    try:
        # Start the uvicorn server
        cmd = [
            sys.executable, "-m", "uvicorn",
            "app:app",
            "--host", "0.0.0.0",
            "--port", "43826",
            "--reload"
        ]
        
        print("🔧 Running command:", " ".join(cmd))
        print("=" * 50)
        
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\n🛑 Service stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start service: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
