"""
FastAPI HTTP service for DroidRun - Async task execution with callback support
"""

import asyncio
import logging
import uuid
from datetime import datetime
from typing import Optional
from contextlib import asynccontextmanager
import httpx
from fastapi import FastAPI, HTTPException

from models import (
    RunTaskRequest,
    StandardResponse,
    CallbackRequest,
    TaskStatus,
    StopTaskRequest
)
from task_manager import TaskManager
from config import settings
from droidrun.agent.droid import DroidAgent
from llama_index.llms.deepseek import DeepSeek

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global task manager
task_manager = TaskManager()

# HTTP client for callbacks
http_client = None


@asynccontextmanager
async def lifespan(_: FastAPI):
    """Manage application lifespan"""
    global http_client

    # Startup
    logger.info("🚀 Starting DroidRun HTTP Service")
    http_client = httpx.AsyncClient(timeout=30.0)

    yield

    # Shutdown
    logger.info("🛑 Shutting down DroidRun HTTP Service")
    if http_client:
        await http_client.aclose()
    await task_manager.cleanup()


# Initialize FastAPI app
app = FastAPI(
    title="DroidRun HTTP Service",
    description="Async task execution service for Android automation",
    version="1.0.0",
    lifespan=lifespan
)


async def execute_droid_task(
    job_id: str,
    content: str,
    device_serial: str,
    callback_url: Optional[str] = None,
    eac_id: Optional[str] = None,
    app_id: Optional[str] = None,
    nonce: Optional[str] = None
):
    """
    Execute a DroidRun task asynchronously
    """
    try:
        logger.info(f"🤖 Starting task execution for job {job_id}")

        # Update task status to running
        await task_manager.update_task_status(job_id, TaskStatus.RUNNING)

        # Create LLM instance
        llm = DeepSeek(
            model=settings.DEEPSEEK_MODEL,
            api_key=settings.DEEPSEEK_API_KEY,
            api_base=settings.DEEPSEEK_API_BASE,
            temperature=settings.DEEPSEEK_TEMPERATURE
        )

        # Create and run DroidAgent
        agent = DroidAgent(
            goal=content,
            llm=llm,
            max_steps=settings.MAX_STEPS,
            device_serial=device_serial,
            timeout=settings.TASK_TIMEOUT,
            max_retries=settings.MAX_RETRIES
        )

        # Execute the task
        result = await agent.run()

        # Determine success status
        success = result.get('success', False)
        reason = result.get('reason', '')

        if success:
            logger.info(f"✅ Task {job_id} completed successfully")
            await task_manager.update_task_status(job_id, TaskStatus.COMPLETED, reason)
            state = 1  # Success
        else:
            logger.error(f"❌ Task {job_id} failed: {reason}")
            await task_manager.update_task_status(job_id, TaskStatus.FAILED, reason)
            state = 3  # Failure

        # Send callback if URL provided
        if callback_url:
            await send_callback(
                callback_url=callback_url,
                job_id=job_id,
                eac_id=eac_id,
                state=state,
                message=reason,
                app_id=app_id,
                nonce=nonce
            )

    except asyncio.CancelledError:
        logger.warning(f"⚠️ Task {job_id} was cancelled")
        await task_manager.update_task_status(job_id, TaskStatus.CANCELLED, "Task was cancelled")

        # Send failure callback if URL provided
        if callback_url:
            await send_callback(
                callback_url=callback_url,
                job_id=job_id,
                eac_id=eac_id,
                state=3,
                message="Task was cancelled",
                app_id=app_id,
                nonce=nonce
            )
        raise

    except Exception as e:
        error_msg = str(e)
        logger.error(f"❌ Task {job_id} failed with exception: {error_msg}")
        await task_manager.update_task_status(job_id, TaskStatus.FAILED, error_msg)

        # Send failure callback if URL provided
        if callback_url:
            await send_callback(
                callback_url=callback_url,
                job_id=job_id,
                eac_id=eac_id,
                state=3,
                message=error_msg,
                app_id=app_id,
                nonce=nonce
            )


async def send_callback(
    callback_url: str,
    job_id: str,
    eac_id: Optional[str],
    state: int,
    message: str,
    app_id: Optional[str] = None,
    nonce: Optional[str] = None
):
    """
    Send callback notification to the specified URL
    """
    try:
        callback_data = CallbackRequest(
            v="1.0.0",
            auth={
                "appId": app_id or "droidrun",
                "nonce": nonce or str(uuid.uuid4())
            },
            arg={
                "eacId": eac_id or "droidrun-service",
                "jobId": job_id,
                "eventType": "Autonomous",
                "state": state,
                "msg": message
            }
        )

        logger.info(f"📞 Sending callback for job {job_id} to {callback_url}")

        response = await http_client.post(
            callback_url,
            json=callback_data.model_dump(),
            headers={"Content-Type": "application/json"}
        )

        if response.status_code == 200:
            logger.info(f"✅ Callback sent successfully for job {job_id}")
        else:
            logger.warning(f"⚠️ Callback failed for job {job_id}: {response.status_code}")

    except Exception as e:
        logger.error(f"❌ Failed to send callback for job {job_id}: {str(e)}")


@app.post("/eac/aia/run-task", response_model=StandardResponse)
async def run_task(request: RunTaskRequest):
    """
    Submit a task for asynchronous execution
    """
    try:
        job_id = request.arg.jobId
        content = request.arg.content
        device_serial = request.arg.device

        logger.info(f"📝 Received task request for job {job_id}")

        # Validate device serial
        if not device_serial:
            raise HTTPException(status_code=400, detail="Device serial is required")

        # Check if task already exists
        if await task_manager.task_exists(job_id):
            raise HTTPException(status_code=409, detail="Task with this job ID already exists")

        # Create task record
        await task_manager.create_task(
            job_id=job_id,
            content=content,
            device_serial=device_serial,
            eac_id=request.arg.eacId,
            callback_url=getattr(request.arg, 'callbackUrl', None)
        )

        # Create and register the background task
        task = asyncio.create_task(
            execute_droid_task(
                job_id=job_id,
                content=content,
                device_serial=device_serial,
                callback_url=getattr(request.arg, 'callbackUrl', None),
                eac_id=request.arg.eacId,
                app_id=request.auth.appId,
                nonce=request.auth.nonce
            )
        )

        # Register the task for cancellation support
        await task_manager.register_running_task(job_id, task)

        logger.info(f"🚀 Task {job_id} queued for execution")

        return StandardResponse(
            code=0,
            msg="Task submitted successfully",
            data={}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error submitting task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/eac/aia/stop-task", response_model=StandardResponse)
async def stop_task(request: StopTaskRequest):
    """
    Stop/cancel a running task
    """
    try:
        job_id = request.arg.jobId

        logger.info(f"🛑 Received stop request for job {job_id}")

        # Check if task exists
        if not await task_manager.task_exists(job_id):
            raise HTTPException(status_code=404, detail="Task not found")

        # Cancel the task
        success = await task_manager.cancel_task(job_id)

        if success:
            logger.info(f"✅ Task {job_id} cancelled successfully")
            return StandardResponse(
                code=0,
                msg="Task cancelled successfully",
                data={}
            )
        else:
            logger.warning(f"⚠️ Task {job_id} could not be cancelled (may have already completed)")
            return StandardResponse(
                code=1,
                msg="Task could not be cancelled",
                data={}
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error stopping task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=43826, reload=True)
