"""
Configuration settings for the FastAPI DroidRun service
"""

import os


class Settings:
    """Application settings"""

    # Server settings
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "43826"))

    # DeepSeek LLM settings
    DEEPSEEK_MODEL: str = os.getenv("DEEPSEEK_MODEL", "deepseek-v3-250324")
    DEEPSEEK_API_KEY: str = os.getenv("DEEPSEEK_API_KEY", "1444135e-36b0-4f40-9130-7524530ea32c")
    DEEPSEEK_API_BASE: str = os.getenv("DEEPSEEK_API_BASE", "https://ark.cn-beijing.volces.com/api/v3")
    DEEPSEEK_TEMPERATURE: float = float(os.getenv("DEEPSEEK_TEMPERATURE", "0.2"))

    # DroidAgent settings
    MAX_STEPS: int = int(os.getenv("MAX_STEPS", "15"))
    TASK_TIMEOUT: int = int(os.getenv("TASK_TIMEOUT", "1000"))
    MAX_RETRIES: int = int(os.getenv("MAX_RETRIES", "3"))

    # Task management settings
    CLEANUP_INTERVAL_HOURS: int = int(os.getenv("CLEANUP_INTERVAL_HOURS", "6"))
    MAX_TASK_AGE_HOURS: int = int(os.getenv("MAX_TASK_AGE_HOURS", "24"))

    # Logging settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # HTTP client settings
    CALLBACK_TIMEOUT: int = int(os.getenv("CALLBACK_TIMEOUT", "30"))

    def __init__(self):
        """Validate required settings"""
        if not self.DEEPSEEK_API_KEY:
            raise ValueError("DEEPSEEK_API_KEY environment variable is required")


# Global settings instance
settings = Settings()
