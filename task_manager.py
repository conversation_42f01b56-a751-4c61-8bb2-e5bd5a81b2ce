"""
Task manager for handling async task execution and status tracking
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Optional, Set
from models import TaskInfo, TaskStatus

logger = logging.getLogger(__name__)


class TaskManager:
    """
    Manages task execution, status tracking, and cancellation
    """
    
    def __init__(self):
        self.tasks: Dict[str, TaskInfo] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.cancelled_tasks: Set[str] = set()
        self._lock = asyncio.Lock()
    
    async def create_task(
        self,
        job_id: str,
        content: str,
        device_serial: str,
        eac_id: Optional[str] = None,
        callback_url: Optional[str] = None
    ) -> TaskInfo:
        """
        Create a new task record
        """
        async with self._lock:
            if job_id in self.tasks:
                raise ValueError(f"Task with job_id {job_id} already exists")
            
            now = datetime.now()
            task_info = TaskInfo(
                job_id=job_id,
                content=content,
                device_serial=device_serial,
                status=TaskStatus.PENDING,
                created_at=now,
                updated_at=now,
                eac_id=eac_id,
                callback_url=callback_url
            )
            
            self.tasks[job_id] = task_info
            logger.info(f"📝 Created task record for job {job_id}")
            return task_info
    
    async def task_exists(self, job_id: str) -> bool:
        """
        Check if a task exists
        """
        async with self._lock:
            return job_id in self.tasks
    
    async def get_task(self, job_id: str) -> Optional[TaskInfo]:
        """
        Get task information
        """
        async with self._lock:
            return self.tasks.get(job_id)
    
    async def update_task_status(
        self,
        job_id: str,
        status: TaskStatus,
        result_message: Optional[str] = None
    ):
        """
        Update task status and result message
        """
        async with self._lock:
            if job_id not in self.tasks:
                logger.warning(f"⚠️ Attempted to update non-existent task {job_id}")
                return
            
            task = self.tasks[job_id]
            task.status = status
            task.updated_at = datetime.now()
            
            if result_message:
                task.result_message = result_message
            
            logger.info(f"📊 Updated task {job_id} status to {status}")
            
            # Clean up running task reference if completed
            if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                if job_id in self.running_tasks:
                    del self.running_tasks[job_id]
    
    async def register_running_task(self, job_id: str, task: asyncio.Task):
        """
        Register a running asyncio task for cancellation support
        """
        async with self._lock:
            self.running_tasks[job_id] = task
            logger.debug(f"🔄 Registered running task for job {job_id}")
    
    async def cancel_task(self, job_id: str) -> bool:
        """
        Cancel a running task
        """
        async with self._lock:
            # Check if task exists
            if job_id not in self.tasks:
                logger.warning(f"⚠️ Attempted to cancel non-existent task {job_id}")
                return False
            
            task_info = self.tasks[job_id]
            
            # Check if task is in a cancellable state
            if task_info.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                logger.warning(f"⚠️ Task {job_id} is not in a cancellable state: {task_info.status}")
                return False
            
            # Mark as cancelled
            self.cancelled_tasks.add(job_id)
            
            # Cancel the asyncio task if it's running
            if job_id in self.running_tasks:
                running_task = self.running_tasks[job_id]
                if not running_task.done():
                    running_task.cancel()
                    logger.info(f"🛑 Cancelled running task for job {job_id}")
                else:
                    logger.info(f"ℹ️ Task {job_id} was already completed")
            
            # Update status
            await self.update_task_status(job_id, TaskStatus.CANCELLED, "Task was cancelled by user")
            
            return True
    
    async def is_cancelled(self, job_id: str) -> bool:
        """
        Check if a task has been cancelled
        """
        async with self._lock:
            return job_id in self.cancelled_tasks
    
    async def get_all_tasks(self) -> Dict[str, TaskInfo]:
        """
        Get all tasks (for debugging/monitoring)
        """
        async with self._lock:
            return self.tasks.copy()
    
    async def get_tasks_by_status(self, status: TaskStatus) -> Dict[str, TaskInfo]:
        """
        Get tasks filtered by status
        """
        async with self._lock:
            return {
                job_id: task for job_id, task in self.tasks.items()
                if task.status == status
            }
    
    async def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """
        Clean up old completed tasks to prevent memory leaks
        """
        async with self._lock:
            now = datetime.now()
            to_remove = []
            
            for job_id, task in self.tasks.items():
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    age_hours = (now - task.updated_at).total_seconds() / 3600
                    if age_hours > max_age_hours:
                        to_remove.append(job_id)
            
            for job_id in to_remove:
                del self.tasks[job_id]
                self.cancelled_tasks.discard(job_id)
                logger.info(f"🧹 Cleaned up old task {job_id}")
            
            if to_remove:
                logger.info(f"🧹 Cleaned up {len(to_remove)} old tasks")
    
    async def cleanup(self):
        """
        Cleanup all resources
        """
        async with self._lock:
            # Cancel all running tasks
            for job_id, task in self.running_tasks.items():
                if not task.done():
                    task.cancel()
                    logger.info(f"🛑 Cancelled task {job_id} during cleanup")
            
            # Wait for all tasks to complete
            if self.running_tasks:
                await asyncio.gather(*self.running_tasks.values(), return_exceptions=True)
            
            # Clear all data
            self.tasks.clear()
            self.running_tasks.clear()
            self.cancelled_tasks.clear()
            
            logger.info("🧹 Task manager cleanup completed")
    
    async def get_stats(self) -> Dict[str, int]:
        """
        Get task statistics
        """
        async with self._lock:
            stats = {
                "total": len(self.tasks),
                "pending": 0,
                "running": 0,
                "completed": 0,
                "failed": 0,
                "cancelled": 0
            }
            
            for task in self.tasks.values():
                stats[task.status.value] += 1
            
            return stats
