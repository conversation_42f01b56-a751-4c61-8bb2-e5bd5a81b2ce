[project]
name = "droidrun"
version = "0.2.0"
description = "A framework for controlling Android devices through LLM agents"
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
]
dependencies = [
    "click>=8.1.0",
    "rich>=13.0.0",
    "pydantic>=2.0.0",
    "aiofiles>=23.0.0",
    "openai>=1.0.0",
    "anthropic>=0.7.0",
    "pillow>=10.0.0",
    "python-dotenv>=1.0.0",
    "llama-index",
    "llama-index-callbacks-arize-phoenix",
    "arize-phoenix",
    "llama-index-llms-openai",
    "llama-index-llms-gemini",
    "llama-index-llms-deepseek",
    "llama-index-llms-anthropic",
    "llama-index-llms-ollama"
]
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Information Technology",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Testing",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Communications :: Chat",
    "Topic :: Software Development :: Testing :: Acceptance",
    "Topic :: Software Development :: Quality Assurance",
    "Topic :: System :: Emulators",
    "Topic :: Utilities",
]

[project.urls]
Homepage = "https://github.com/droidrun/droidrun"
"Bug Tracker" = "https://github.com/droidrun/droidrun/issues"
Documentation = "https://docs.droidrun.ai/"

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.0.0",
    "bandit>=1.7.0",
    "safety>=2.0.0"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["droidrun"]

[project.scripts]
droidrun = "droidrun.cli.main:cli"

[tool.ruff]
line-length = 100
target-version = "py310"

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
] 
